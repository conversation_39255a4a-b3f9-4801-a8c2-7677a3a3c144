import { ref } from 'vue'
import { useUpdate } from '~/compositions/useUpdate'
import { hoverShowImg } from '~/utils/hoverShowImg'
import { checkLocalClockNotification } from '~/compositions/useClock'
import { checkMaintenance, isMaintenance } from '~/compositions/useMaintenance'
import { initPayWay } from '~/compositions/useSetting'
import { setCurrRole } from '~/compositions/useCurrRole'

export const currWeb = ref<'wbl' | 'm_wbl' | 'aj3' | 'j3sh' | ''>('')
const aj3Host = 'www.aijx3.cn'
const j3shPcHost = 'pc.jx3sh.com'
const j3shPhoneHost = 'www.jx3sh.com'
const wblPcHost = 'jx3.seasunwbl.com'
const wblPhoneHost = 'm.seasunwbl.com'

function setCurrWeb() {
  const host = window.location.host
  if (host === wblPcHost) {
    currWeb.value = 'wbl'
  }
  else if (host === wblPhoneHost) {
    currWeb.value = 'm_wbl'
  }
  else if (host === aj3Host && window.location?.pathname?.includes('/w/')) {
    currWeb.value = 'aj3'
  }
  else if (host === j3shPcHost || host === j3shPhoneHost) {
    if (window?.location?.pathname === '/h5/pages/goods/wbl' || window?.location?.hash?.includes('#/detail/wbl'))
      currWeb.value = 'j3sh'
    else
      currWeb.value = ''
  }
  else {
    currWeb.value = ''
  }
}

export async function useInit() {
  setCurrWeb()
  if (currWeb.value === 'wbl' || currWeb.value === 'm_wbl') {
    await setCurrRole()
    // 检查更新
    const { checkUpdate } = useUpdate()
    checkUpdate()
    initPayWay()
    if (isMaintenance()) {
      await checkMaintenance()
    }
    else {
    // 图片悬浮显示预览图初始化
      hoverShowImg.init()
      // 检查本地闹钟通知，如果有闹钟通知，会在闹钟时间触发通知
      checkLocalClockNotification()
    }
  }
}
