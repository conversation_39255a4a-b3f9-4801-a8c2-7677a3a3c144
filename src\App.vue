<script setup lang="ts">
import { useQuasar } from 'quasar'
import MainMenu from '~/components/MainMenu.vue'
import { useMyIcon } from '~/compositions/useIcon'
import Icons from '~/components/Icons.vue'
import { useInit } from '~/compositions/useInit'

const $q = useQuasar()
$q.iconMapFn = iconName => useMyIcon(iconName)
useInit()
</script>

<template>
  <MainMenu />
  <Icons />
</template>

<style scoped>
</style>
