import { ref } from 'vue'
import QRCode from 'qrcode'
import {
  encryptPwd,
  getLocalStorage,
  getSessionStorage,
  handlingErrors,
  isRetryOrder,
  setLocalStorage,
  setSessionStorage,
} from '~/utils/utils'
import { createOrderApi, getAdditionalServiceApi, loginApi, payApi, setTokenApi } from '~/api/wbl'
import initGeetest4 from '~/utils/gt'
import { errNotify, successNotify, warningNotify } from '~/compositions/useNotify'
import type {
  AdditionalService,
  AdditionalServiceOptions,
  DeliveryInfo,
  GeetestObj,
  GoodsDetail,
  OrderCallback,
  PayCallback,
} from '~/type'
import { openSelectRoleDialog } from '~/compositions/useSelectRole'
import {
  loginCaptchaId,
  orderCaptchaId,
  orderCaptchaTime,
  orderRetryDelayTime,
  orderRetryTimes,
  payRetryDelayTime,
  payRetryTimes,
} from '~/compositions/useSetting'
// 当前选择框
export const currSelect = ref('')
// 是否是角色多开
export const isRoleMultiOpen = ref(false)
// 是否是外观多开
export const isSkinMultiOpen = ref(false)
export const isShowLoginDialog = ref(false)
export const currQrcode = ref('')
const account = ref('')
const pwd = ref('')
export function useLoginDialog() {
  return {
    account,
    pwd,
  }
}

export const zfbUrls = ref<string[]>([])

function getLocalAccountTokens() {
  const list = getSessionStorage('accountTokenList', []) as AccountToken[]
  const filterList = list.filter(item => item.expire > Date.now())
  if (filterList.length !== list.length)
    setSessionStorage('accountTokenList', filterList)
  if (zfbUrls.value.length !== filterList.length)
    zfbUrls.value = Array.from({ length: filterList.length }).fill('') as string[]
  return filterList
}
// 多开验证码对象
export const multiGeetestObj = ref< GeetestObj[] >([])
interface LocalGeetestObj {
  geetestObj: GeetestObj
  expire: number
}
function getLocalGeetest() {
  const list = getSessionStorage('multiGeetestObj', []) as LocalGeetestObj[]
  const filterList = list.filter(item => item.expire > Date.now())
  if (filterList.length !== list.length)
    setSessionStorage('multiGeetestObj', filterList)
  multiGeetestObj.value = filterList.map(item => item.geetestObj)
}
getLocalGeetest()

function setLocalGeetest(geetestObj: GeetestObj) {
  const list = getSessionStorage('multiGeetestObj', []) as LocalGeetestObj[]
  list.push({ geetestObj, expire: Date.now() + 60 * orderCaptchaTime * 1000 })
  setSessionStorage('multiGeetestObj', list)
}

function removeLocalGeetestByCaptchaId(captchaId: string) {
  const list = getSessionStorage('multiGeetestObj', []) as LocalGeetestObj[]
  const filterList = list.filter(item => item?.geetestObj?.captcha_id !== captchaId)
  setSessionStorage('multiGeetestObj', filterList)
}

function getGeetestObj() {
  if (multiGeetestObj.value.length === 0) {
    errNotify(`验证码数量不足。`)
  }
  else {
    const geetestObj = multiGeetestObj.value.pop() as GeetestObj
    if (geetestObj) {
      removeLocalGeetestByCaptchaId(geetestObj.captcha_id)
      return geetestObj
    }
  }
  return null
}

const currLoginInfo = ref()
interface AccountToken {
  account: string
  token: string
  expire: number
  separationServiceFee?: number
  isSelectedRole?: boolean
  roleInfo?: string
}
// 多开账号token列表
export const accountTokenList = ref<AccountToken[]>(getLocalAccountTokens())
// 是否打开登录框
export const isOpenLoginDialog = ref(false)

export async function selectRole(account: AccountToken) {
  if (!account?.isSelectedRole)
    openSelectRoleDialog(account.token)
  else
    successNotify('该账号已选择角色')
}

export const currAccount = ref(`账号：${getSessionStorage('account', '首次登录账号')}`)

export function setToken(token: string, account?: string) {
  // 如果token在列表中已存在， 则不设置
  if (token === '')
    return
  const index = accountTokenList.value.findIndex(item => item.token === token)
  setSessionStorage('currToken', token)
  if (index === -1) {
    setLocalStorage('token', token)
    if (account) {
      setSessionStorage('account', account)
      currAccount.value = `账号：${account}`
    }
  }
}

async function login(captcha: GeetestObj) {
  const res = await loginApi(currLoginInfo.value.account, encryptPwd(currLoginInfo.value.password), captcha)
  if (res.code === 1) {
    account.value = ''
    pwd.value = ''
    isShowLoginDialog.value = false
    // 先检查是否已经登录过， 如果已经登录过， 则替换token及过期时间
    const index = accountTokenList.value.findIndex(item => item.account === currLoginInfo.value.account)
    if (index !== -1) {
      accountTokenList.value[index].token = res.data.ts_session_id
      accountTokenList.value[index].expire = (new Date().getTime() + 3600 * 1000)
      zfbUrls.value[index] = ''
    }
    else {
      accountTokenList.value.push({ account: currLoginInfo.value.account, token: res.data.ts_session_id, expire: (new Date().getTime() + 3600 * 1000) })
      zfbUrls.value.push('')
    }
    isRoleMultiOpen.value = false
    isSkinMultiOpen.value = false
    setSessionStorage('accountTokenList', accountTokenList.value)
    successNotify(`账号 [ ${currLoginInfo.value.account} ] 登录成功`)
  }
  else {
    errNotify(res.msg)
  }
}

function loginEmbed(captchaObj: any) {
  captchaObj.onReady(() => {

  }).onSuccess(async () => {
    const captcha = captchaObj.getValidate() as GeetestObj
    await login(captcha)
  }).onError((e: any) => {
    handlingErrors(e, `初始化验证码失败，请重试。`)
  })
  captchaObj.showBox()
}
export const loadAccountCallBack = ref<Function | null>(null)

function handlerEmbedSuccess(geetestTemp: GeetestObj) {
  multiGeetestObj.value.push(geetestTemp)
  setLocalGeetest(geetestTemp)
  if (multiGeetestObj.value.length < accountTokenList.value.length + 1) {
    loadAccountCaptcha()
  }
  else {
    if (loadAccountCallBack.value) {
      loadAccountCallBack.value()
      loadAccountCallBack.value = null
    }
    if (currSelect.value === 'role')
      isRoleMultiOpen.value = true
    else if (currSelect.value === 'skin')
      isSkinMultiOpen.value = true
    currSelect.value = ''
  }
}
function handlerEmbed(captchaObj: any) {
  captchaObj.onReady(() => {

  }).onSuccess(() => {
    const geetestTemp = captchaObj.getValidate() as GeetestObj
    handlerEmbedSuccess(geetestTemp)
  }).onError((e: any) => {
    handlingErrors(e, `初始化验证码失败，请重试。`)
  })
  captchaObj.showBox()
}

export async function loadAccountCaptcha() {
  initGeetest4({ product: 'bind', captchaId: orderCaptchaId }, handlerEmbed)
  await new Promise(resolve => setTimeout(resolve, 1000))
}

export function loginWbl(account: string, password: string) {
  currLoginInfo.value = { account, password }
  if (account && password)
    initGeetest4({ product: 'bind', captchaId: loginCaptchaId }, loginEmbed)
  else
    warningNotify('请输入账号密码')
}

function showQrcode(str: string, index: number) {
  const opts = {
    errorCorrectionLevel: 'Q',
    quality: 1,
    margin: 1,
    width: 300,
    color: {
      dark: '#1688ff',
      light: '#fff',
    },
  }
  // 将二维码渲染到 canvas 中，然后将 canvas 添加到目标元素
  QRCode.toDataURL(str, opts, (err: any, url: string) => {
    if (err) {
      errNotify('生成二维码失败')
      return
    }
    if (index === -1)
      currQrcode.value = url
    else
      zfbUrls.value[index] = url
  })
}

async function pay(orderId: string, type: number, token: string, index: number) {
  let retryCount = 0
  let res
  let errorOccurred = false

  do {
    try {
      if (retryCount > 0)
        await new Promise(resolve => setTimeout(resolve, payRetryDelayTime))

      res = await payApi(orderId, type, token)
      errorOccurred = false // 如果请求成功，重置 errorOccurred 为 false
    }
    catch (error) {
    // 如果捕获到错误，设置 errorOccurred 为 true
      errorOccurred = true
    }
    retryCount++
  } while ((errorOccurred || res?.code !== 1) && retryCount <= payRetryTimes.value)
  if (res?.code !== 1) {
    errNotify(res.msg)
  }
  else {
    const pay_attach = (res.data as PayCallback).pay_attach
    showQrcode(pay_attach, index)
  }
}

export async function multiAdditionalService(orderId: string) {
  // 网页登录用户的分离费
  const originToken = getLocalStorage('token')
  const originRes = await getAdditionalServiceApi(orderId, originToken)
  const originData = originRes.data.list as AdditionalService[]
  const originSeparationServiceFee = originData.find(item => item.name === 'separation_service')?.value
  setSessionStorage('originSeparationServiceFee', originSeparationServiceFee)
  // 插件登录用户的分离费
  for (const item of accountTokenList.value) {
    const index = accountTokenList.value.indexOf(item)
    if (item.separationServiceFee !== undefined)
      continue
    const res = await getAdditionalServiceApi(orderId, item.token)
    if (res.code === 1) {
      const data = res.data.list as AdditionalService[]
      accountTokenList.value[index].separationServiceFee = data.find(item => item.name === 'separation_service')?.value
    }
    else {
      errNotify(`账号 [ ${item.account} ] 获取角色分离费失败：${res.msg}`)
    }
  }
  setSessionStorage('accountTokenList', accountTokenList.value)
}

async function createOrderAndNotify(type: number, row: GoodsDetail, additionalServiceSum: number, transferServiceObj: AdditionalServiceOptions | undefined, additionalService: AdditionalService[] | undefined, currAccount: AccountToken, i: number, deliveryInfo?: DeliveryInfo) {
  let geetestObj = getGeetestObj() as GeetestObj
  if (!geetestObj) {
    errNotify('验证码数量不足，创建订单失败。')
    return
  }
  let retryCount = 0
  let res
  let errorOccurred = false
  let lastErrMsg = ''
  do {
    try {
      if (retryCount > 0) {
      // 如果是重试，并且上一次请求出错，等待一段时间
        await new Promise(resolve => setTimeout(resolve, orderRetryDelayTime))
      }
      if (lastErrMsg.includes('验证码')) {
        geetestObj = getGeetestObj() as GeetestObj
        lastErrMsg = ''
      }
      res = await createOrderApi(row, geetestObj, type, additionalServiceSum, transferServiceObj, additionalService, currAccount.token, deliveryInfo)
      errorOccurred = false // 如果请求成功，重置 errorOccurred 为 false
      if (res?.code !== 1)
        lastErrMsg = res?.msg || ''
    }
    catch (error) {
    // 如果捕获到错误，设置 errorOccurred 为 true
      console.error(error)
      errorOccurred = true
    }
    retryCount++
  } while ((errorOccurred || isRetryOrder(res?.code)) && retryCount <= orderRetryTimes.value)
  if (res.code === 1) {
    successNotify(`账号 [ ${currAccount.account} ] 订单创建成功`)
    const orderId = (res.data as OrderCallback).order_id
    await new Promise(resolve => setTimeout(resolve, 100))
    await pay(orderId, type, currAccount.token, i)
  }
  else {
    errNotify(`账号 [ ${currAccount.account} ] 订单创建失败：${res.msg}`)
  }
}

function getAdditionalInfoByToken(additionalServiceSum: number, additionalService: AdditionalService[] | undefined, currAccount?: AccountToken) {
  let tempAdditionalServiceSum = JSON.parse(JSON.stringify(additionalServiceSum)) as number
  const tempAdditionalService = JSON.parse(JSON.stringify(additionalService)) as AdditionalService[]
  const tempSepFee = additionalService?.find(item => item.name === 'separation_service')?.value || 0
  if (!currAccount) {
    const originFee = getSessionStorage('originSeparationServiceFee')
    if (tempSepFee !== originFee) {
      tempAdditionalServiceSum = tempAdditionalServiceSum - tempSepFee + originFee
      tempAdditionalService.find(item => item.name === 'separation_service')!.value = originFee
    }
  }
  else if (currAccount.separationServiceFee !== undefined && tempAdditionalService !== undefined) {
    if (tempSepFee !== currAccount.separationServiceFee) {
      tempAdditionalServiceSum = tempAdditionalServiceSum - tempSepFee + currAccount.separationServiceFee
      tempAdditionalService.find(item => item.name === 'separation_service')!.value = currAccount.separationServiceFee
    }
  }
  return { tempAdditionalServiceSum, tempAdditionalService }
}

export async function multiCreateOrder(row: GoodsDetail, type: number, additionalServiceSum: number, transferServiceObj: AdditionalServiceOptions | undefined, additionalService: AdditionalService[] | undefined, deliveryInfo?: DeliveryInfo) {
  let geetestObj = getGeetestObj() as GeetestObj
  if (!geetestObj) {
    errNotify('验证码数量不足，创建订单失败。')
    return
  }
  const token = getLocalStorage('token')
  let retryCount = 0
  let res
  let errorOccurred = false
  let lastErrMsg = ''
  do {
    try {
      if (retryCount > 0)
        await new Promise(resolve => setTimeout(resolve, orderRetryDelayTime))
      if (lastErrMsg.includes('验证码')) {
        geetestObj = getGeetestObj() as GeetestObj
        lastErrMsg = ''
      }
      if (type === 2) {
        const { tempAdditionalServiceSum, tempAdditionalService } = getAdditionalInfoByToken(additionalServiceSum, additionalService)
        res = await createOrderApi(row, geetestObj, type, tempAdditionalServiceSum, transferServiceObj, tempAdditionalService, token)
      }
      else {
        res = await createOrderApi(row, geetestObj, type, additionalServiceSum, transferServiceObj, additionalService, token, deliveryInfo)
      }
      errorOccurred = false // 如果请求成功，重置 errorOccurred 为 false
      if (res?.code !== 1)
        lastErrMsg = res?.msg || ''
    }
    catch (err) {
      console.error(err)
      errorOccurred = true
    }
    retryCount++
  } while ((errorOccurred || isRetryOrder(res?.code)) && retryCount <= orderRetryTimes.value)
  if (res?.code === 1) {
    successNotify(`当前账号订单创建成功`)
    const orderId = (res.data as OrderCallback).order_id
    await pay(orderId, type, token, -1)
  }
  else {
    errNotify(`当前账号订单创建失败：${res.msg}`)
  }
  for (let i = 0; i < accountTokenList.value.length; i++) {
    await new Promise(resolve => setTimeout(resolve, 150))
    const currAccount = accountTokenList.value[i]
    if (type === 2) {
      const { tempAdditionalServiceSum, tempAdditionalService } = getAdditionalInfoByToken(additionalServiceSum, additionalService, currAccount)
      await createOrderAndNotify(type, row, tempAdditionalServiceSum, transferServiceObj, tempAdditionalService, currAccount, i)
    }
    else if (type === 3) {
      await createOrderAndNotify(type, row, additionalServiceSum, transferServiceObj, additionalService, currAccount, i, deliveryInfo)
    }
  }
  await setTokenApi()
}
