<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import RoleSelector from './RoleSelector.vue'
import type { DeliveryInfo, UserRoleInfo } from '~/type'

interface Props {
  serverId: string
  zoneId: string
  disabled?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: DeliveryInfo | null): void
  (e: 'deliveryInfoChanged', info: DeliveryInfo | null): void
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
})

const emit = defineEmits<Emits>()

// 收货方式：1=游戏角色收货，2=万宝楼收货
const deliveryType = ref<number>(2) // 默认选择万宝楼收货
const selectedRole = ref<UserRoleInfo | null>(null)
// @unocss-include
const deliveryOptions = [
  { label: '万宝楼收货', value: 2, icon: 'i-pixelarticons-home', description: '商品将发送到万宝楼，可随时提取' },
  { label: '游戏角色收货', value: 1, icon: 'i-pixelarticons-user', description: '商品将直接发送到指定游戏角色' },
]

// 计算收货地址信息
const deliveryInfo = computed<DeliveryInfo | null>(() => {
  if (deliveryType.value === 2) {
    // 万宝楼收货
    return {
      delivery_destination: 2,
    }
  }
  else if (deliveryType.value === 1 && selectedRole.value) {
    // 游戏角色收货
    return {
      delivery_destination: 1,
      role_id: selectedRole.value.id,
      zone_id: props.zoneId,
      server_id: props.serverId,
    }
  }
  return null
})

// 监听收货信息变化
watch(deliveryInfo, (newInfo) => {
  emit('update:modelValue', newInfo)
  emit('deliveryInfoChanged', newInfo)
}, { immediate: true })

// 监听收货方式变化，重置角色选择
watch(deliveryType, (newType) => {
  if (newType === 2)
    selectedRole.value = null
})

// 角色选择变化
function onRoleSelected(role: UserRoleInfo) {
  selectedRole.value = role
}

// 验证是否可以提交
const isValid = computed(() => {
  if (deliveryType.value === 2)
    return true // 万宝楼收货总是有效
  else if (deliveryType.value === 1)
    return selectedRole.value !== null // 游戏角色收货需要选择角色

  return false
})

defineExpose({
  isValid,
  deliveryInfo,
})
</script>

<template>
  <div class="delivery-address-selector">
    <div class="text-h6 q-mb-md">
      选择收货地址
    </div>

    <!-- 收货方式选择 -->
    <div class="q-mb-lg">
      <div class="text-subtitle2 q-mb-sm">
        收货方式
      </div>
      <q-option-group
        v-model="deliveryType"
        :options="deliveryOptions"
        :disable="disabled"
        color="teal"
        type="radio"
      >
        <template #label="opt">
          <div class="row items-center q-gutter-sm">
            <q-icon :name="opt.icon" size="sm" />
            <div>
              <div class="text-weight-medium">
                {{ opt.label }}
              </div>
              <div class="text-caption text-grey-6">
                {{ opt.description }}
              </div>
            </div>
          </div>
        </template>
      </q-option-group>
    </div>

    <!-- 角色选择（仅在选择游戏角色收货时显示） -->
    <div v-if="deliveryType === 1" class="q-mb-md">
      <div class="text-subtitle2 q-mb-sm">
        选择收货角色
      </div>
      <RoleSelector
        v-model="selectedRole"
        :server-id="serverId"
        :disabled="disabled"
        placeholder="请选择要接收商品的角色"
        @role-selected="onRoleSelected"
      />
      <div v-if="deliveryType === 1 && !selectedRole" class="text-caption text-orange q-mt-xs">
        <q-icon name="i-pixelarticons-info" size="xs" class="q-mr-xs" />
        请选择一个角色来接收外观商品
      </div>
    </div>

    <!-- 收货信息预览 -->
    <div v-if="deliveryInfo" class="delivery-preview q-pa-md bg-grey-1 rounded-borders">
      <div class="text-subtitle2 q-mb-sm">
        <q-icon name="i-pixelarticons-check" color="teal" class="q-mr-xs" />
        收货信息确认
      </div>
      <div class="text-body2">
        <div v-if="deliveryType === 2" class="text-teal">
          <q-icon name="i-pixelarticons-home" class="q-mr-xs" />
          商品将发送到万宝楼
        </div>
        <div v-else-if="deliveryType === 1 && selectedRole" class="text-teal">
          <q-icon name="i-pixelarticons-user" class="q-mr-xs" />
          商品将发送到角色：{{ selectedRole.name }} (Lv.{{ selectedRole.level }})
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.delivery-address-selector {
  min-width: 400px;
}

.delivery-preview {
  border-left: 4px solid var(--q-teal);
}

.q-option-group .q-radio {
  margin-bottom: 12px;
}
</style>
