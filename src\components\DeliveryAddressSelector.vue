<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue'
import RoleSelector from './RoleSelector.vue'
import { getBaseInfoApi, getGatewaysApi, getUserRolesApi } from '~/api/wbl'
import { getLocalStorage, getSessionStorage, setLocalStorage, setSessionStorage } from '~/utils/utils'
import { errNotify } from '~/compositions/useNotify'
import type { DeliveryInfo, GatewayInfo, ServerInfo, UserRoleInfo, baseRoleInfo } from '~/type'

interface Props {
  disabled?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: DeliveryInfo | null): void
  (e: 'deliveryInfoChanged', info: DeliveryInfo | null): void
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
})

const emit = defineEmits<Emits>()

// 状态变量
const loading = ref(false)
const baseInfo = ref<baseRoleInfo | null>(null)
const currentServerId = ref<string>('')
const currentZoneId = ref<string>('')
const roles = ref<UserRoleInfo[]>([])

// 从 localStorage 恢复用户偏好的收货方式
const savedDeliveryType = getLocalStorage('delivery_preference')
const deliveryType = ref<number>(savedDeliveryType ? Number(savedDeliveryType) : 2) // 默认选择万宝楼收货
const selectedRole = ref<UserRoleInfo | null>(null)
// @unocss-include
const deliveryOptions = [
  { label: '万宝楼收货', value: 2, icon: 'i-pixelarticons-home', description: '商品将发送到万宝楼，可随时提取' },
  { label: '游戏角色收货', value: 1, icon: 'i-pixelarticons-user', description: '商品将直接发送到指定游戏角色' },
]

// 初始化数据
onMounted(async () => {
  await initializeData()
})

// 获取用户基础信息和角色数据
async function initializeData() {
  loading.value = true
  try {
    // 1. 获取用户基础信息
    await loadBaseInfo()

    // 2. 如果有基础信息，获取区服ID并加载角色列表
    if (baseInfo.value) {
      await loadServerInfo()
      if (currentServerId.value) {
        await loadRoles()
        // 3. 自动预选当前用户的角色
        autoSelectCurrentRole()
      }
    }
  }
  catch (error) {
    console.error('初始化收货地址数据失败:', error)
    errNotify('获取用户信息失败，请稍后重试')
  }
  finally {
    loading.value = false
  }
}

// 获取用户基础信息
async function loadBaseInfo() {
  // 先尝试从缓存获取
  const cachedBaseInfo = getSessionStorage('baseInfo')
  if (cachedBaseInfo) {
    baseInfo.value = JSON.parse(cachedBaseInfo)
    return
  }

  // 缓存中没有，调用API获取
  try {
    const res = await getBaseInfoApi()
    if (res.code === 1 && res.data) {
      baseInfo.value = res.data
      // 缓存到 sessionStorage
      setSessionStorage('baseInfo', JSON.stringify(res.data))
    }
  }
  catch (error) {
    console.error('获取基础信息失败:', error)
  }
}

// 获取区服信息并匹配当前用户的区服ID
async function loadServerInfo() {
  if (!baseInfo.value)
    return

  try {
    const res = await getGatewaysApi()
    if (res.code === 1 && res.data?.list) {
      // 通过 zone_name 和 server_name 匹配找到对应的区服ID
      const targetZone = res.data.list.find((zone: GatewayInfo) =>
        zone.zone_name === baseInfo.value?.zone_name,
      )

      if (targetZone) {
        currentZoneId.value = targetZone.zone_id
        const targetServer = targetZone.servers.find((server: ServerInfo) =>
          server.server_name === baseInfo.value?.server_name,
        )

        if (targetServer)
          currentServerId.value = targetServer.server_id
      }
    }
  }
  catch (error) {
    console.error('获取区服信息失败:', error)
  }
}

// 获取角色列表
async function loadRoles() {
  if (!currentServerId.value)
    return

  try {
    const token = getLocalStorage('token') || ''
    const res = await getUserRolesApi(currentServerId.value, token)

    if (res.code === 1 && res.data?.list)
      roles.value = res.data.list.filter(role => role.freeze === 0) // 过滤掉冻结的角色
  }
  catch (error) {
    console.error('获取角色列表失败:', error)
  }
}

// 自动预选当前用户的角色
function autoSelectCurrentRole() {
  if (!baseInfo.value?.role_name || roles.value.length === 0)
    return

  const currentRole = roles.value.find(role =>
    role.name === baseInfo.value?.role_name,
  )

  if (currentRole)
    selectedRole.value = currentRole
}

// 计算收货地址信息
const deliveryInfo = computed<DeliveryInfo | null>(() => {
  if (deliveryType.value === 2) {
    // 万宝楼收货
    return {
      delivery_destination: 2,
    }
  }
  else if (deliveryType.value === 1 && selectedRole.value && currentZoneId.value && currentServerId.value) {
    // 游戏角色收货
    return {
      delivery_destination: 1,
      role_id: selectedRole.value.id,
      zone_id: currentZoneId.value,
      server_id: currentServerId.value,
    }
  }
  return null
})

// 监听收货信息变化
watch(deliveryInfo, (newInfo) => {
  emit('update:modelValue', newInfo)
  emit('deliveryInfoChanged', newInfo)
}, { immediate: true })

// 监听收货方式变化，重置角色选择并保存用户偏好
watch(deliveryType, (newType) => {
  if (newType === 2)
    selectedRole.value = null

  // 保存用户偏好到 localStorage
  setLocalStorage('delivery_preference', newType.toString())
})

// 角色选择变化
function onRoleSelected(role: UserRoleInfo) {
  selectedRole.value = role
}

// 验证是否可以提交
const isValid = computed(() => {
  if (deliveryType.value === 2)
    return true // 万宝楼收货总是有效
  else if (deliveryType.value === 1)
    return selectedRole.value !== null // 游戏角色收货需要选择角色

  return false
})

defineExpose({
  isValid,
  deliveryInfo,
})
</script>

<template>
  <div class="delivery-address-selector">
    <div class="text-h6 q-mb-md">
      选择收货地址
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="text-center q-py-lg">
      <q-spinner-dots size="lg" color="teal" />
      <div class="text-caption text-grey-6 q-mt-sm">
        正在获取用户信息...
      </div>
    </div>

    <!-- 用户信息显示 -->
    <div v-else-if="baseInfo" class="user-info q-mb-md q-pa-sm bg-blue-1 rounded-borders">
      <div class="text-caption text-grey-7 q-mb-xs">
        当前账号信息
      </div>
      <div class="text-body2">
        <q-icon name="i-pixelarticons-user" class="q-mr-xs" />
        {{ baseInfo.role_name }} - {{ baseInfo.server_name }} ({{ baseInfo.zone_name }})
      </div>
    </div>

    <!-- 收货方式选择 -->
    <div class="q-mb-lg">
      <div class="text-subtitle2 q-mb-sm">
        收货方式
      </div>
      <q-option-group
        v-model="deliveryType"
        :options="deliveryOptions"
        :disable="props.disabled || loading"
        color="teal"
        type="radio"
      >
        <template #label="opt">
          <div class="row items-center q-gutter-sm">
            <q-icon :name="opt.icon" size="sm" />
            <div>
              <div class="text-weight-medium">
                {{ opt.label }}
              </div>
              <div class="text-caption text-grey-6">
                {{ opt.description }}
              </div>
            </div>
          </div>
        </template>
      </q-option-group>
    </div>

    <!-- 角色选择（仅在选择游戏角色收货时显示） -->
    <div v-if="deliveryType === 1" class="q-mb-md">
      <div class="text-subtitle2 q-mb-sm">
        选择收货角色
      </div>
      <RoleSelector
        v-model="selectedRole"
        :server-id="currentServerId"
        :disabled="props.disabled || loading"
        placeholder="请选择要接收商品的角色"
        @role-selected="onRoleSelected"
      />
      <div v-if="deliveryType === 1 && !selectedRole" class="text-caption text-orange q-mt-xs">
        <q-icon name="i-pixelarticons-info" size="xs" class="q-mr-xs" />
        请选择一个角色来接收外观商品
      </div>
    </div>

    <!-- 收货信息预览 -->
    <div v-if="deliveryInfo" class="delivery-preview q-pa-md bg-grey-1 rounded-borders">
      <div class="text-subtitle2 q-mb-sm">
        <q-icon name="i-pixelarticons-check" color="teal" class="q-mr-xs" />
        收货信息确认
      </div>
      <div class="text-body2">
        <div v-if="deliveryType === 2" class="text-teal">
          <q-icon name="i-pixelarticons-home" class="q-mr-xs" />
          商品将发送到万宝楼
          <div class="text-caption text-grey-6 q-mt-xs">
            区服：{{ baseInfo?.zone_name }} - {{ baseInfo?.server_name }}
          </div>
        </div>
        <div v-else-if="deliveryType === 1 && selectedRole" class="text-teal">
          <q-icon name="i-pixelarticons-user" class="q-mr-xs" />
          商品将发送到角色：{{ selectedRole.name }} (Lv.{{ selectedRole.level }})
          <div class="text-caption text-grey-6 q-mt-xs">
            区服：{{ baseInfo?.zone_name }} - {{ baseInfo?.server_name }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.delivery-address-selector {
  min-width: 400px;
}

.delivery-preview {
  border-left: 4px solid var(--q-teal);
}

.q-option-group .q-radio {
  margin-bottom: 12px;
}
</style>
