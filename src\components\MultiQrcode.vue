<script setup lang="ts">
import { currQrcode, isRoleMultiOpen, zfbUrls } from '~/compositions/useMultiOpen'

defineProps<{ qrcodeUrl: string }>()
</script>

<template>
  <div class="multi-qrcode">
    <div class="qrcode">
      <q-img :src="currQrcode" />
    </div>
    <div v-for="(item, index) in zfbUrls" :key="index" class="qrcode">
      <q-img :src="item" />
    </div>
  </div>
  <div v-if="isRoleMultiOpen" class="w-100% pt-40px text-center font-size-12px">
    <span>多开抢号角色分离费已根据是否是新账号计算，以实际支付为准</span>
  </div>
</template>

<style scoped>
.multi-qrcode {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 50px;
}
.qrcode {
  display: flex;
  width: 200px;
  height: 200px;
  background-color: #fff;
  box-shadow: 0 0 4px 4px #1688ff;
  position: relative;
  border-radius: 4px;
}

.qrcode:before {
  position: absolute;
  content: '支付宝二维码区域';
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
</style>
