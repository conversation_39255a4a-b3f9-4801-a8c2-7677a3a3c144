import { handlingErrors } from '~/utils/utils'

const defaultOptions = {
  method: 'GET',
  credentials: 'include', // 携带跨域Cookie
  headers: {
    'Content-Type': 'application/json',
  },
}

async function handleResponse(response: Response, isThrowErr: boolean) {
  if (!response.ok) {
    handlingErrors(new Error(`${response.status} ${response.statusText}`))
    return
  }

  const data = await response.json()
  if (isThrowErr) {
    return data
  }
  else if (data?.code !== 1) {
    handlingErrors(new Error(`${data.msg}`))
    return
  }

  return data
}

export const httpWithErr = async function customFetch(url: string, options?: any, isThrowErr = true) {
  // 合并传入的选项和默认选项
  options = { ...defaultOptions, ...options }
  try {
    const response = await fetch(url, options)
    return handleResponse(response, isThrowErr)
  }
  catch (error) {
    handlingErrors(error)
  }
}

export const http = async function customFetch(url: string, options?: any) {
  return httpWithErr(url, options, false)
}

// 专门用于需要session认证的请求，确保携带必要的cookie
export const httpWithSession = async function sessionFetch(url: string, options?: any, isThrowErr = true) {
  // 合并传入的选项和默认选项
  options = { ...defaultOptions, ...options }

  // 确保设置必要的cookie（如果浏览器环境支持）
  if (typeof document !== 'undefined') {
    // 检查是否已有必要的cookie，如果没有则尝试设置
    const cookies = document.cookie
    if (!cookies.includes('m_gray_switch=1'))
      document.cookie = 'm_gray_switch=1; path=/; domain=.seasunwbl.com'

    if (!cookies.includes('m_gray_switch_=1'))
      document.cookie = 'm_gray_switch_=1; path=/; domain=.seasunwbl.com'
  }

  try {
    const response = await fetch(url, options)
    return handleResponse(response, isThrowErr)
  }
  catch (error) {
    handlingErrors(error)
  }
}
