import { handlingErrors } from '~/utils/utils'

const defaultOptions = {
  method: 'GET',
  credentials: 'include', // 携带跨域Cookie
  headers: {
    'Content-Type': 'application/json',
  },
}

async function handleResponse(response: Response, isThrowErr: boolean) {
  if (!response.ok) {
    handlingErrors(new Error(`${response.status} ${response.statusText}`))
    return
  }

  const data = await response.json()
  if (isThrowErr) {
    return data
  }
  else if (data?.code !== 1) {
    handlingErrors(new Error(`${data.msg}`))
    return
  }

  return data
}

export const httpWithErr = async function customFetch(url: string, options?: any, isThrowErr = true) {
  // 合并传入的选项和默认选项
  options = { ...defaultOptions, ...options }
  try {
    const response = await fetch(url, options)
    return handleResponse(response, isThrowErr)
  }
  catch (error) {
    handlingErrors(error)
  }
}

export const http = async function customFetch(url: string, options?: any) {
  return httpWithErr(url, options, false)
}
