// Description: Define the type of FollowItem

export interface FollowItem {
  // 结束时间
  date: number
  // 角色门派
  role_sect: string
  // 角色等级
  role_level: number
  // 3:公示期 5:在售期 6:已售出 7:已下架
  state: number
  // 2:角色 3:外观
  type: number
  // 商品图标
  goods_icon_url: string
  // 单价
  single_count_price_rmb: number
  // 大区
  zone_name: string
  // 剩余时间
  remaining_time: number
  // 服务器
  server_name: string
  // 商品价格
  goods_price: number
  // 关注时的价格
  follow_goods_price: number
  // 商品id
  consignment_id: string
  // 角色名
  role_name: string
  // 商品名
  goods_name: string
  // 商品类型
  goods_type_name: string
  // 30日均价
  avg_price_30: number
  // 关注时间
  attention_date: number
  // 卖家名
  creator_name: string
  // 结束时间
  end_time?: number
  // 是否发送通知
  clock?: boolean
  // 通知倒计时定时器
  clockTimer?: NodeJS.Timeout | null
  // 关注数
  followed_num?: number
  // 30日交易热度
  sellCount?: number
  // 30日实装热度
  followHeat?: number
  // 30日最低均价
  minAvgPrice?: number
  // 在售期最低价
  minSalePrice?: number
  // 公示期最低价
  minPublicPrice?: number
  // 外观基础信息
  appearanceBaseInfo?: AppearanceBaseInfo
  // 角色基础信息
  role_summary_data?: RoleSummaryData
}

export interface RoleSummaryData {
  appearance: {
    hair: number
    back: number
    horse: number
    waist: number
    backCloak: number
    shopExterior: number
  }
  tags: string[]
  base: {
    justice: number
    prestige: number
    arena3V3Level: number
    arena2V2Level: number
    arena5V5Level: number
    contribution: number
    militaryRank: number
    admireReputeCount: number
  }
}

// 商品详情
export interface GoodsDetail {
  // 商品图标
  thumb: string
  attrs: {
    // 商品索引
    item_index?: number
    // 商品类型
    item_type?: number
    // 外观类型
    appearance_type_name?: string
    // 角色等级
    role_level?: number
    // 角色装分
    role_equipment_point?: number
    // 江湖资历
    role_experience_point?: number
    // 百战精力
    role_hundred_war_spirit?: number
    // 百战耐力
    role_hundred_war_endurance?: number
    // 角色门派
    role_sect?: string
    // 角色阵营
    role_camp?: string
    // 角色体型
    role_shape?: string
  }
  // 商品标签
  tags: any[]
  // 商品状态
  state: number
  // 单位大小
  single_unit_size: number
  // 剩余单位数量
  remain_unit_count: number
  // 大区id
  zone_id: string
  // 大区名
  zone_name: string
  // 服务器id
  server_id: string
  // 服务器名
  server_name: string
  // 卖家角色名
  seller_role_name: string
  // 是否关注
  is_followed: number
  // 关注数量
  followed_num: number
  // 商品id
  consignment_id: string
  // 是否新商品
  is_new: number
  // 单价
  single_unit_price: number
  // 剩余时间
  remaining_time: number
  // 商品信息
  info: string
  // 账号类型
  account_type: number
  // 访问者
  visitor: string
  // 请求id
  request_id: string
  // 会话id
  ts_session_id: string
}

// 附加服务选项
export interface AdditionalServiceOptions {
  // 选项名
  name: string
  // 选项价格
  value: number
  // 服务器id
  server_id?: string
  // 服务器名
  server_name?: string
  // 大区id
  zone_id?: string
  // 大区名
  zone_name?: string
}

// 附加服务
export interface AdditionalService {
  // 服务描述
  desc: string
  // 是否必选
  must_selected: boolean
  // 服务名
  name: string
  // 服务价格
  value: number
  // 是否选择
  selected: boolean
  // 服务选项
  option: AdditionalServiceOptions[]
}

// 创建订单返回参数
export interface OrderCallback {
  // 购买类型
  buyType: number
  // 金额
  money: number
  // 商品类型
  order_type: number
  // 数量
  quantity: any
  // 状态
  state: number
  // 单位数量
  unit_count: number
  // 大区id
  zone_id: string
  // 下单时间
  date: string
  // 服务器id
  server_id: string
  // 订单id
  order_id: string
  // 剩余时间
  remaining_time: number
  // 游戏id
  game_id: string
  // 请求id
  request_id: string
  // 会话id
  ts_session_id: string
}

export interface baseRoleInfo {
  role_name: string
  zone_name: string
  role_level: number
  server_name: string
  pf: string
  account_uid: string
  account: string
  credit_level: number
  credit_title: string
  credit_score: number
  account_game_state: number
  identity_info_state: number
  anti_addiction_state: number
  accept_agreement: boolean
  balance: number
  mobile: string
  second_password_state: number
  cash_out_account_state: number
  merchant_account_state: number
}

// 用户角色信息（用于角色列表API）
export interface UserRoleInfo {
  // 是否冻结
  freeze: number
  // 角色名
  name: string
  // 角色等级
  level: number
  // 角色ID
  id: string
}

// 用户角色列表API响应
export interface UserRolesResponse {
  code: number
  msg: string
  data: {
    list: UserRoleInfo[]
  }
}

// 收货地址信息接口
export interface DeliveryInfo {
  // 收货方式：1=游戏角色收货，2=万宝楼收货
  delivery_destination: number
  // 角色ID（当delivery_destination=1时必填）
  role_id?: string
  // 区服ID（当delivery_destination=1时必填）
  zone_id?: string
  // 服务器ID（当delivery_destination=1时必填）
  server_id?: string
}

// 支付回调对象
export interface PayCallback {
  // 支付订单id
  pay_order_id: string
  // 支付方式
  pay_handle_method: string
  // 订单id
  order_id: string
  // 支付附加信息
  pay_attach: string
  // 支付剩余时间
  pay_remaining_time: number
  // 请求id
  request_id: string
  // 会话id
  ts_session_id: string
}

// 表格列头类型
export interface TableColumns<T> {
  name: string
  align?: 'left' | 'right' | 'center' | undefined
  label: string
  field: string | ((row: T) => any)
  style?: string | undefined
  sortable?: boolean | undefined
  format?: undefined | ((val: any, row: T) => any)
  classes?: string | ((row: T) => any) | undefined
  headerStyle?: string | undefined
  sort?: undefined | ((a: any, b: any) => number)
}

// 验证码对象
export type GeetestObj = {
  captcha_id: string
  lot_number: string
  pass_token: string
  gen_time: string
  captcha_output: string
} | null

export interface ConsigneeInfo {
  zone_id: string
  server_id: string
  is_change_group?: number
  // 收货方式：1=游戏角色收货，2=万宝楼收货
  delivery_destination?: number
  // 角色ID（当delivery_destination=1时必填）
  role_id?: string
}

export interface ServiceFeeInfo {
  separation_service_fee: number
  transfer_service_fee: number
  change_group_service_fee?: number
}

export interface OrderInfo {
  req_id: string
  game_id: string
  account_uid?: string
  buy_type: number
  total_price: number
  total_quantity: number
  total_unit_count: number
  order_type: number
  role_transfer_agm?: number
  service_fee_info: ServiceFeeInfo
  consignee_info: ConsigneeInfo
  list: [
    {
      count: number
      id: string
    },
  ]
}

export interface OrderStatus {
  process_list: any
  discount_amount: number
  money: number
  date: string
  goods_amount: number
  order_amount: number
  unit_count: number
  order_id: string
  retry_pay_order_list: []
  // 1:待支付 2:已关闭 3:已支付 4:未知 5:已发货 6:未知 7:已退款
  state: number
  pay_way_name: ''
  remaining_time: number
  server_name: string
  game_name: string
  account: string
  zone_name: string
  role_name: string
  goods_list: any
  request_id: string
  ts_session_id: string
}

export interface trendItem {
  // 时间
  dt: string
  // 商品index
  item_index: number
  // 商品类型
  item_type: number
  // 日均价
  avg_price: number
  // 游戏码
  game_code: string
}

// 交易趋势数据
export interface TradeData {
  // 最大价格
  max_price: number
  // 30 日均价
  avg_price_30: number
  // 30 日交易热度
  sell_cnt_30: number
  // 30 日实装热度
  follow_heat_30: number
  // 交易趋势list
  trend_list: trendItem[]
}

interface AppearanceBaseItem {
  appearanceType: string
  icon: string
  itemID: number
  itemType: number
  name: string
}

export interface AppearanceBaseInfo {
  getSource: string // 获取途径
  itemBase: AppearanceBaseItem // 基础信息
  rewards: string // 积分
  sellDate: string // 上架时间
  shopWorth: number // 商城价值
  subItems: AppearanceBaseItem[] // 子物品
}

// 分类信息
interface SubCategory {
  category: string
  count: number
  subCategoryMap: Record<string, SubCategory>
}

interface Category {
  category: string
  count: number
  subCategoryMap: Record<string, SubCategory>
}

export type CategoryInfoWithCountMap = Record<string, Category>

// 饰品分类
export interface TrinketCategoryItem {
  category: string
  trinketSubCategoryItems: [
    {
      count: number
      name: string
      total: number
    },
  ]
}

interface GoodsItem {
  role_name: string
  single_unit_price: number
  unit_count: number
  total_price: number
  consignment_id: string
  info: string
  thumb: string
  attrs: any
  tags: string[]
}

export interface GoodsOrder {
  buyType: number
  quantity: any
  money: number
  date: string
  state: number
  unit_count: number
  zone_name: string
  order_id: string
  server_name: string
  role_name: string
  pay_way_name: string
  remaining_time: number
  goods_list: GoodsItem[]
}
