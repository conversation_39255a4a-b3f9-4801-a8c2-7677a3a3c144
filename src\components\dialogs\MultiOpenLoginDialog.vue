<script setup lang="ts">
import { computed } from 'vue'
import CardHeader from '~/components/CardHeader.vue'
import {
  accountTokenList,
  currAccount,
  currSelect,
  isOpenLoginDialog,
  isRoleMultiOpen,
  isShowLoginDialog,
  isSkinMultiOpen,
  loadAccountCaptcha,
  multiGeetestObj,
  selectRole,
  zfbUrls,
} from '~/compositions/useMultiOpen'
import LoginDialog from '~/components/dialogs/LoginDialog.vue'
import { errNotify } from '~/compositions/useNotify'
import { getLocalStorage, getSessionStorage, setSessionStorage } from '~/utils/utils'
import IconBtn from '~/components/IconBtn.vue'
import { logoutApi, setTokenApi } from '~/api/wbl'
import { setCurrRole } from '~/compositions/useCurrRole'

function onchange(type: 'role' | 'skin') {
  currSelect.value = ''
  if (accountTokenList.value.length === 0) {
    errNotify('请先添加账号')
    isSkinMultiOpen.value = false
    isRoleMultiOpen.value = false
    return
  }
  if (type === 'skin') {
    const isOpenSkinMultiOpen = accountTokenList.value.every(item => item?.isSelectedRole)
    if (!isOpenSkinMultiOpen) {
      errNotify('请先完成所有账号的角色选择')
      isSkinMultiOpen.value = false
      isRoleMultiOpen.value = false
      return
    }
  }

  if (multiGeetestObj.value.length >= (accountTokenList.value.length + 1)) {
    if (type === 'role')
      isSkinMultiOpen.value = false
    else
      isRoleMultiOpen.value = false
  }
  else {
    if (type === 'role' && !isRoleMultiOpen.value)
      return
    if (type === 'skin' && !isSkinMultiOpen.value)
      return
    errNotify('至少完成账号等量的验证码输入后才能开启多开')
    currSelect.value = type
    loadAccountCaptcha()
    isRoleMultiOpen.value = false
    isSkinMultiOpen.value = false
  }
}

async function removeAccountToken(account: string) {
  const token = accountTokenList.value.find(item => item.account === account)?.token as string
  accountTokenList.value = accountTokenList.value.filter(item => item.account !== account)
  zfbUrls.value.pop()
  await logoutApi(token)
  setSessionStorage('accountTokenList', accountTokenList.value)
  await setTokenApi()
}

const isCurrToken = computed(() => {
  const tempToken = getLocalStorage('token')
  const currToken = getSessionStorage('currToken')
  const index = accountTokenList.value.findIndex(item => item.token === tempToken)
  return index === -1 && tempToken === currToken
})

function isActive(token: string) {
  return token === getSessionStorage('currToken')
}

async function changeAccount(token?: string) {
  let isChange
  if (token)
    isChange = !isActive(token)
  else
    isChange = !isCurrToken.value
  if (isChange) {
    if (token) {
      await setCurrRole(token)
    }
    else {
      const token = getLocalStorage('token')
      await setCurrRole(token)
    }
    window.location.reload()
  }
  else {
    errNotify('已经是当前账号了')
  }
}
</script>

<template>
  <q-dialog v-model="isOpenLoginDialog" persistent transition-show="scale" transition-hide="scale">
    <q-card min-w-700px>
      <CardHeader title="多开账号" @close="isOpenLoginDialog = false" />
      <q-card-section>
        <q-list bordered separator class="rounded-borders">
          <q-item v-for="item in accountTokenList" :key="item.account" v-ripple clickable :active="isActive(item.token)" active-class="text-green">
            <q-item-section @click="changeAccount(item.token)">
              <q-item-label lines="1">
                账号：{{ item.account }}
              </q-item-label>
            </q-item-section>
            <q-item-section side>
              <div flex="~ row">
                <div flex="~ col justify-end " @click="selectRole(item)">
                  <div>
                    <template v-if="Date.now() < item.expire">
                      <q-icon size="sm" name="i-pixelarticons-check" color="green" />
                      <span ml-10px class="text-green">已登录，失效时间：{{ new Date(item.expire).toLocaleString() }}</span>
                    </template>
                    <template v-else>
                      <q-icon size="sm" name="i-pixelarticons-close" color="red" />
                      <span ml-10px class="text-red">已失效</span>
                    </template>
                  </div>
                  <div>
                    <template v-if="item?.isSelectedRole">
                      <span>当前角色：{{ item?.roleInfo }}</span>
                    </template>
                    <template v-else>
                      <span>单击此处选择角色</span>
                    </template>
                  </div>
                </div>
                <IconBtn color="red" icon="i-pixelarticons-trash" tooltip="退出登录" flat @click="removeAccountToken(item.account)" />
              </div>
            </q-item-section>
          </q-item>
          <q-item v-ripple clickable active-class="text-green" :active="isCurrToken">
            <q-item-section @click="changeAccount()">
              <q-item-label lines="1">
                {{ currAccount }}
              </q-item-label>
            </q-item-section>
            <q-item-section side>
              <div>
                <q-icon size="sm" name="i-pixelarticons-check" color="green" />
                <span ml-10px class="text-green">已登录</span>
              </div>
            </q-item-section>
          </q-item>
        </q-list>
      </q-card-section>
      <q-card-section>
        <div text-gray>
          如果多开抢号/抢外观结束后，账号自动切换到别的号上，可以单击该界面上面左边的 “账号：xxx” 来切换账号。
        </div>
      </q-card-section>
      <q-card-actions align="right">
        <span :class="multiGeetestObj.length >= accountTokenList.length + 1 ? 'text-green' : 'text-red'">当前验证码：{{ multiGeetestObj.length }}/{{ accountTokenList.length + 1 }}</span>
        <q-checkbox v-model="isSkinMultiOpen" ml-10px color="teal" label="多开抢外观" @update:model-value="onchange('skin')" />
        <q-checkbox v-model="isRoleMultiOpen" ml-10px color="teal" label="多开抢号" @update:model-value="onchange('role')" />
        <q-space />
        <q-btn label="输入验证码" color="teal" @click="loadAccountCaptcha" />
        <q-btn label="添加账号" color="teal" @click="isShowLoginDialog = true" />
      </q-card-actions>
    </q-card>
    <LoginDialog v-model="isShowLoginDialog" />
  </q-dialog>
</template>

<style scoped>

</style>
