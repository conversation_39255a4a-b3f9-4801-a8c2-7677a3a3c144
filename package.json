{"name": "jx3-wbl-tools", "type": "module", "version": "1.2.10", "private": true, "description": "万宝楼小助手", "author": "方仟仟", "license": "MIT", "homepage": "https://github.com/liuc-c/tampermonkey-scripts#readme", "repository": {"type": "git", "url": "git+https://github.com/liuc-c/tampermonkey-scripts.git"}, "bugs": {"url": "https://github.com/liuc-c/tampermonkey-scripts/issues"}, "scripts": {"dev": "vite", "build": "vue-tsc --noEmit && vite build --minify esbuild", "release": "vite build --minify esbuild && tcb storage upload dist/ qianqian/ -e script-login-7gl4kl3kda68f130 -r gz", "preview": "vite preview", "lint": "eslint .", "test": "vitest", "test:unit": "vitest", "typecheck": "vue-tsc --noEmit", "up": "taze major -I"}, "dependencies": {"canvas-confetti": "^1.9.2", "jsencrypt": "^3.3.2", "qrcode": "^1.5.3", "quasar": "^2.15.2", "vue": "^3.4.21"}, "devDependencies": {"@antfu/eslint-config": "^2.13.3", "@iconify/json": "^2.2.200", "@quasar/vite-plugin": "^1.6.0", "@types/node": "^20.12.7", "@unocss/eslint-config": "^0.59.2", "@unocss/reset": "^0.59.2", "@vitejs/plugin-vue": "^5.0.4", "@vue/compiler-sfc": "^3.4.21", "@vueuse/core": "^10.9.0", "eslint": "^9.0.0", "eslint-plugin-format": "^0.1.0", "eslint-ts-patch": "^8.57.0-0", "taze": "^0.13.3", "typescript": "^5.4.5", "unocss": "^0.59.2", "vite": "^5.2.8", "vite-plugin-monkey": "^3.5.2", "vite-tsconfig-paths": "^4.3.2", "vitest": "^1.5.0", "vue-tsc": "^2.0.13"}, "lint-staged": {"*": "eslint --fix"}}