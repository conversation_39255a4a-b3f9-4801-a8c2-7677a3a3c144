import { computed, nextTick, ref } from 'vue'
import QRCode from 'qrcode'
import { Screen } from 'quasar'
import type {
  AdditionalService,
  AdditionalServiceOptions,
  DeliveryInfo,
  FollowItem,
  GoodsDetail,
  OrderCallback,
  OrderStatus,
  PayCallback,
} from '~/type'

import { errNotify, infoNotify, successNotify, warningNotify } from '~/compositions/useNotify'
import { isHaveRole, isRetryOrder } from '~/utils/utils'
import { setGoodsStateById, useRob } from '~/compositions/useRob'
import { createOrderApi, getAdditionalServiceApi, getDetailInfoApi, getOrderStatusApi, payApi } from '~/api/wbl'
import { openZfbDialogCallback, useCaptcha } from '~/compositions/useCaptcha'
import { fireworks } from '~/utils/fireworks'
import { sendEndOfPeriodNotification } from '~/compositions/useClock'
import {
  accountTokenList,
  isRoleMultiOpen,
  isSkin<PERSON>ultiOpen,
  loadAccountCallBack,
  loadAccountCaptcha,
  multiAdditionalService,
  multiCreateOrder,
  multiGeetestObj,
  setToken,
  zfbUrls,
} from '~/compositions/useMultiOpen'
import {
  currPayWay,
  orderCaptchaTime,
  orderRetryDelayTime,
  orderRetryTimes,
  payRetryDelayTime,
  payRetryTimes,
  preRobTime,
} from '~/compositions/useSetting'
import { openSelectRoleDialog } from '~/compositions/useSelectRole'

const { closeRob } = useRob()
// 是否显示支付宝二维码弹出框
export const isShowZfbDialog = ref(false)
// 当前商品信息
const currentRow = ref<FollowItem>()
// 当前商品详细详细
const currentDetail = ref<GoodsDetail>()
// 二维码链接
const qrcodeUrl = ref('')
// 验证码对象
const { geetestObj, removeLocalCaptcha, loadCaptcha, getCaptcha, captchaIsExpire } = useCaptcha()
// 倒计时文本
const countdownText = ref(``)
// 对话框当前步骤，selectAdditionalService: 选择附加服务，pay: 支付
const currentStep = ref('selectAdditionalService')
// 附加服务
const additionalService = ref<AdditionalService[]>()
// 转服费用以及服务器名
const transferServiceObj = ref<AdditionalServiceOptions>()
// 收货地址信息（仅外观商品使用）
const deliveryInfo = ref<DeliveryInfo | null>(null)
const isMaintenance = ref(false)
const maintenanceType = ref('web')
/**
 * 显示支付宝二维码
 * @param str 支付宝链接
 */
function showQrcode(str: string) {
  const opts = {
    errorCorrectionLevel: 'Q',
    quality: 1,
    margin: 1,
    width: 300,
    color: {
      dark: '#1688ff',
      light: '#fff',
    },
  }
  // 将二维码渲染到 canvas 中，然后将 canvas 添加到目标元素
  QRCode.toDataURL(str, opts, (err: any, url: string) => {
    if (err) {
      errNotify('生成二维码失败')
      return
    }
    qrcodeUrl.value = url
  })
}
/**
 * 附加服务费总和
 */
const additionalServiceSum = computed(() => {
  return additionalService.value?.reduce((acc: number, curr: AdditionalService) => {
    if (curr.selected) {
      if (curr.name === 'transfer_service')
        acc += transferServiceObj.value?.value || 0
      else
        acc += curr.value
    }
    return acc
  }, 0) || 0
})

/**
 * 收货地址信息变化处理
 */
function onDeliveryInfoChanged(info: DeliveryInfo | null) {
  deliveryInfo.value = info
}

/**
 * 创建订单
 * @param row 商品详细信息
 * @param type 商品类型 2:角色 3:外观
 */
async function createOrder(row: GoodsDetail, type: number) {
  let additionalSum = 0
  // 如果是角色商品，获取附加服务费，计算附加服务费总和
  if (type === 2)
    additionalSum = additionalServiceSum.value || 0
  if ((isRoleMultiOpen.value && type === 2) || (isSkinMultiOpen.value && type === 3)) {
    await multiCreateOrder(row, type, additionalSum, transferServiceObj.value, additionalService.value, deliveryInfo.value || undefined)
  }
  else {
    let retryCount = 0
    let res
    let errorOccurred = false
    let lastErrMsg = ''
    let captcha = getCaptcha()
    do {
      try {
        if (retryCount > 0)
          await new Promise(resolve => setTimeout(resolve, orderRetryDelayTime))
        if (lastErrMsg.includes('验证码')) {
          captcha = getCaptcha()
          lastErrMsg = ''
        }
        if (!captcha) {
          errNotify('验证码不足')
          return
        }
        res = await createOrderApi(row, captcha, type, additionalSum, transferServiceObj.value, additionalService.value, '', deliveryInfo.value || undefined)
        errorOccurred = false // 如果请求成功，重置 errorOccurred 为 false
        if (res?.code !== 1)
          lastErrMsg = res?.msg || ''
      }
      catch (error) {
        console.error(error)
        errorOccurred = true
      }
      retryCount++
    } while ((errorOccurred || isRetryOrder(res?.code)) && retryCount <= orderRetryTimes.value)

    if (res.code === 1) {
      await pay(row, (res.data as OrderCallback).order_id, type)
    }
    else {
      errNotify(res.msg)
      closeZfbDialog()
      toHome()
    }
  }
}

async function pay(row: GoodsDetail, orderId: string, type: number) {
  // 重试次数
  let retryCount = 0
  let res
  let errorOccurred = false

  do {
    try {
      if (retryCount > 0) {
      // 如果是重试，并且上一次请求出错，等待一段时间
        await new Promise(resolve => setTimeout(resolve, payRetryDelayTime))
      }
      res = await payApi(orderId, type)
      errorOccurred = false // 如果请求成功，重置 errorOccurred 为 false
    }
    catch (error) {
    // 如果捕获到错误，设置 errorOccurred 为 true
      errorOccurred = true
    }
    retryCount++
  } while ((errorOccurred || res?.code !== 1) && retryCount <= payRetryTimes.value)
  if (res?.code === 1) {
    const pay_attach = (res.data as PayCallback).pay_attach
    if (currPayWay.value === 'phone' || Screen.xs) {
      window.location.href = pay_attach
    }
    else {
      showQrcode(pay_attach)
      if ('Notification' in window) {
        if (Notification.permission === 'granted') {
          const notification = new Notification('扫码提醒', {
            body: '请使用支付宝扫码支付',
            icon: 'https://img.alicdn.com/tfs/TB1qEwuzrj1gK0jSZFOXXc7GpXa-32-32.ico',
          })
          // 设置定时器，在指定时间后关闭通知
          setTimeout(() => {
            notification.close()
          }, 30 * 1e3)
        }
      }
    }
    checkOrderStatus(row.consignment_id, row.info, orderId, type)
  }
  else {
    errNotify(res.msg)
  }
}
/**
 * 检查订单状态，如果订单状态为 已支付或已退款或已发货，则关闭支付宝弹框
 * @param consignment_id 商品id
 * @param goodsName 商品名称 用于上报订单状态
 * @param orderId 订单id
 * @param orderType 商品类型 2:角色 3:外观
 */
function checkOrderStatus(consignment_id: string, goodsName: string, orderId: string, orderType: number) {
  // 如果state为3，则notify('支付成功')，关闭支付宝弹框
  // 关闭支付宝弹框后，接着每隔2秒检查一次订单状态，直到state变为5（已发货）或7（已退款）为止
  let startTime = Date.now()
  let isPaySuccess = false
  const checkStatus = async () => {
    // 检查是否已经超过2分钟
    if (Date.now() - startTime > 2 * 60 * 1000)
      return

    const res = await getOrderStatusApi(orderId, orderType)
    const data = res.data as OrderStatus
    if (data.state === 3 && !isPaySuccess) {
      successNotify(`[ ${orderId} ] 订单已支付成功`)
      isPaySuccess = true
      closeZfbDialog(consignment_id)
    }
    else if (data.state === 2) {
      infoNotify(`[ ${orderId} ] 订单已关闭`)
      closeZfbDialog(consignment_id)
      return
    }
    else if (data.state === 5) {
      successNotify('恭喜小红手成功抢到商品！！！')
      fireworks()
      setGoodsStateById(consignment_id, 6)
      closeZfbDialog(consignment_id)
      return
    }
    else if (data.state === 7) {
      warningNotify(`很遗憾，[ ${orderId} ] 订单已退款`)
      setGoodsStateById(consignment_id, 6)
      closeZfbDialog(consignment_id)
      return
    }
    setTimeout(checkStatus, 2 * 1e3)
  }
  // 在调用 checkStatus 之前，重置开始时间
  startTime = Date.now()
  setTimeout(checkStatus, 2 * 1e3)
}

/**
 * 前置验证
 */
async function beforeOpenDialog(row: FollowItem) {
  // 检查是否有商品信息
  if (!(row && row.consignment_id)) {
    errNotify('无法获取商品信息，请稍后再试')
    return false
  }
  //  5:在售期 3:公示期 ，只有在售期或公示期小于 10 分钟的商品才能抢购 orderCaptchaTime
  const isEligibleForPurchase = row.state === 5 || (row.state === 3 && row?.end_time && row.end_time - Date.now() < orderCaptchaTime * 60 * 1e3)
  if (!isEligibleForPurchase) {
    warningNotify(`请选择 [ 在售期 ] 或 [ 公示期仅剩 ${orderCaptchaTime} 分钟 ] 的商品进行抢购`)
    return false
  }
  // 检查是否有角色
  if (!(await isHaveRole(row.type))) {
    closeRob()
    openSelectRoleDialog()
    return false
  }
  if (isRoleMultiOpen.value && row.type === 2 && multiGeetestObj.value.length < (accountTokenList.value.length + 1)) {
    warningNotify('请先完成所有账号的验证码验证')
    loadAccountCallBack.value = () => openZfbDialog(row)
    await loadAccountCaptcha()
    return false
  }
  if (isRoleMultiOpen.value && row.type === 2 && multiGeetestObj.value.length >= (accountTokenList.value.length + 1))
    return true
  if (isSkinMultiOpen.value && row.type === 3 && multiGeetestObj.value.length < (accountTokenList.value.length + 1)) {
    warningNotify('请先完成所有账号的验证码验证')
    loadAccountCallBack.value = () => openZfbDialog(row)
    await loadAccountCaptcha()
    return false
  }
  if (isSkinMultiOpen.value && row.type === 3 && multiGeetestObj.value.length >= (accountTokenList.value.length + 1))
    return true
  // 检查验证码是否完成
  if (!geetestObj.value?.length) {
    warningNotify('请先完成验证码验证')
    openZfbDialogCallback.value = () => openZfbDialog(row)
    loadCaptcha()
    return false
  }
  // 检查验证码是否过期，row.end_time - lastCaptchaTime > orderCaptchaTime * 60 * 1e3 说明验证码过期
  if (row.state === 3 && row?.end_time && captchaIsExpire(row.end_time)) {
    warningNotify('验证码即将过期，请重新添加验证码')
    openZfbDialogCallback.value = () => openZfbDialog(row)
    loadCaptcha()
    return false
  }
  return true
}
let stopPolling = false
/**
 * 等待抢购
 * @param detail 商品详细信息
 * @param type 商品类型 2:角色 3:外观
 */
async function waitBuy(detail: GoodsDetail, type: number) {
  let isNotification = false
  let remainingTime = detail.remaining_time
  let count = 0
  countdownText.value = `公示期结束倒计时：${remainingTime} 秒`
  let isFirstCheckState = true
  const checkState = async () => {
    if (stopPolling)
      return // 如果需要停止轮询，直接返回
    if (remainingTime > 30 && !isFirstCheckState) {
      count++
      if (count % 10 === 0) {
        const res = await getDetailInfoApi(detail.consignment_id, type)
        const data = res.data as GoodsDetail
        remainingTime = data.remaining_time
      }
      else {
        remainingTime--
      }
      countdownText.value = `公示期结束倒计时：${remainingTime} 秒`
    }
    else {
      if (isFirstCheckState)
        isFirstCheckState = false

      const res = await getDetailInfoApi(detail.consignment_id, type)
      const data = res.data as GoodsDetail

      if (data.state === 6) {
        stopPolling = true
        warningNotify('订单已售出，已停止抢购')
        closeZfbDialog()
        toHome()
        return
      }
      else if (data.state === 7) {
        stopPolling = true
        warningNotify('订单已下架，已停止抢购')
        closeZfbDialog()
        toHome()
        return
      }
      remainingTime = data.remaining_time
      countdownText.value = data.state === 5 ? '请使用支付宝扫描二维码支付' : `公示期结束倒计时：${remainingTime} 秒`
      if (data.state === 5) {
        stopPolling = true
        await createOrder(data, type)
        return
      }
      else if (data.remaining_time <= 6 && !isNotification) {
        sendEndOfPeriodNotification(data.info, data.single_unit_price, data.remaining_time, data.thumb)
        isNotification = true
      }
    }

    if (remainingTime <= 6)
      setTimeout(checkState, preRobTime.value)
    else
      setTimeout(checkState, 1e3)
  }
  await checkState()
}

/**
 * 在售期商品，直接创建订单
 */
async function createOrderInSalePeriod(detail: GoodsDetail, type: number) {
  countdownText.value = '请使用支付宝扫描二维码支付'
  isShowZfbDialog.value = true
  await nextTick(async () => {
    await createOrder(detail, type)
  })
}

/**
 * 公示期商品，等待抢购
 */
async function waitBuyInPublicityPeriod(detail: GoodsDetail, type: number) {
  isShowZfbDialog.value = true
  await nextTick(async () => {
    stopPolling = false
    await waitBuy(detail, type)
  })
}

/**
 * 处理商品类型，如果是角色商品，需要先选择附加服务；如果是外观商品，需要先选择收货地址
 */
async function handleRoleType(row: FollowItem) {
  if (row.type === 2) {
    // 角色商品：获取附加服务
    const res = await getAdditionalServiceApi(row.consignment_id)
    additionalService.value = res.data.list.map((item: AdditionalService) => ({ ...item, selected: item.must_selected }))
    currentStep.value = 'selectAdditionalService'
    if (isRoleMultiOpen.value)
      await multiAdditionalService(row.consignment_id)
  }
  else if (row.type === 3) {
    // 外观商品：需要选择收货地址，初始化空的附加服务
    additionalService.value = []
    deliveryInfo.value = null // 重置收货地址信息
    currentStep.value = 'selectAdditionalService' // 显示收货地址选择界面
  }
  else {
    // 其他类型商品直接进入支付
    currentStep.value = 'pay'
    if (row.state === 5) {
      await createOrderInSalePeriod(currentDetail.value as GoodsDetail, row.type)
    }
    else if (row.state === 3) {
      await waitBuyInPublicityPeriod(currentDetail.value as GoodsDetail, row.type)
    }
    else if (row.state === 6) {
      warningNotify('商品已售出')
      setGoodsStateById(row.consignment_id, 6)
      closeZfbDialog()
    }
    else if (row.state === 7) {
      warningNotify('商品已下架')
      setGoodsStateById(row.consignment_id, 7)
      closeZfbDialog()
    }
  }
}

async function nextStep() {
  // 角色商品的转服服务验证
  const isTransferService = additionalService.value?.find(item => item.name === 'transfer_service')?.selected || false
  if (isTransferService && !transferServiceObj.value) {
    errNotify('请选择区服')
    return
  }

  // 外观商品的收货地址验证
  if (currentRow.value?.type === 3) {
    if (!deliveryInfo.value) {
      errNotify('请选择收货地址')
      return
    }
    if (deliveryInfo.value.delivery_destination === 1 && !deliveryInfo.value.role_id) {
      errNotify('请选择收货角色')
      return
    }
  }

  currentStep.value = 'pay'
  const currentType = currentRow.value?.type || 2
  if (currentRow.value!.state === 5)
    await createOrderInSalePeriod(currentDetail.value as GoodsDetail, currentType)
  else
    await waitBuyInPublicityPeriod(currentDetail.value as GoodsDetail, currentType)
}

async function handleMaintenanceRoleType(consignment_id: string, state: number) {
  const res = await getAdditionalServiceApi(consignment_id)
  additionalService.value = res.data.list.map((item: AdditionalService) => ({ ...item, selected: item.must_selected }))
  currentStep.value = 'pay'
  if (state === 5) {
    await createOrderInSalePeriod(currentDetail.value as GoodsDetail, 2)
  }
  else if (state === 3) {
    await waitBuyInPublicityPeriod(currentDetail.value as GoodsDetail, 2)
  }
  else if (state === 6) {
    warningNotify('商品已售出')
    closeZfbDialog()
    toHome()
  }
  else if (state === 7) {
    warningNotify('商品已下架')
    closeZfbDialog()
    toHome()
  }
}

function toHome() {
  if (isMaintenance.value) {
    removeLocalCaptcha()
    if (maintenanceType.value === 'web')
      window.location.href = 'https://jx3.seasunwbl.com'
    else
      window.location.href = 'https://m.seasunwbl.com/jx3/login.html'
  }
}

export async function openZfbDialogByMaintenance(consignment_id: string, isMobile = false) {
  maintenanceType.value = isMobile ? 'mobile' : 'web'
  isMaintenance.value = true
  qrcodeUrl.value = ''
  const res = await getDetailInfoApi(consignment_id, 2)
  if (res && res.data) {
    const detail = res.data as GoodsDetail
    currentDetail.value = detail
    currentRow.value = {
      appearanceBaseInfo: undefined,
      attention_date: 0,
      avg_price_30: 0,
      clock: false,
      clockTimer: undefined,
      creator_name: '',
      date: 0,
      end_time: 0,
      followHeat: 0,
      follow_goods_price: 0,
      followed_num: 0,
      goods_icon_url: detail.thumb,
      goods_name: detail.info,
      goods_price: detail.single_unit_price,
      goods_type_name: '',
      minAvgPrice: 0,
      minPublicPrice: 0,
      minSalePrice: 0,
      remaining_time: 0,
      role_level: 0,
      role_name: '',
      role_sect: '',
      role_summary_data: undefined,
      sellCount: 0,
      server_name: '',
      single_count_price_rmb: 0,
      state: 0,
      zone_name: '',
      consignment_id,
      type: 2,
    }
    switch (detail.state) {
      case 5:
        await handleMaintenanceRoleType(consignment_id, 5)
        isShowZfbDialog.value = true
        break
      case 3:
        await handleMaintenanceRoleType(consignment_id, 3)
        isShowZfbDialog.value = true
        break
      case 6:
        errNotify('商品已售出')
        toHome()
        break
      case 7:
        errNotify('商品已下架')
        toHome()
        break
      default:
        errNotify('商品状态异常，请重试')
    }
  }
}

export async function openZfbDialog(row: FollowItem) {
  if (!await beforeOpenDialog(row))
    return
  qrcodeUrl.value = ''
  zfbUrls.value = zfbUrls.value.map(() => '')
  currentRow.value = row
  const res = await getDetailInfoApi(row.consignment_id, row.type)
  if (res && res.data) {
    currentDetail.value = res.data as GoodsDetail
    setToken((res.data as GoodsDetail).ts_session_id)
    const detail = res.data as GoodsDetail
    switch (detail.state) {
      case 5:
        await handleRoleType(row)
        isShowZfbDialog.value = true
        break
      case 3:
        await handleRoleType(row)
        isShowZfbDialog.value = true
        break
      case 6:
        errNotify('商品已售出')
        setGoodsStateById(row.consignment_id, 6)
        break
      case 7:
        errNotify('商品已下架')
        setGoodsStateById(row.consignment_id, 7)
        break
      default:
        errNotify('商品状态异常，请重试')
        setGoodsStateById(row.consignment_id, 6)
    }
  }
  else { errNotify('无法获取商品信息，请稍后再试') }
}

function closeZfbDialog(consignment_id = '') {
  if (consignment_id && consignment_id !== currentRow.value?.consignment_id)
    return
  stopPolling = true // 停止轮询抢外观
  isShowZfbDialog.value = false
  currentRow.value = undefined
  additionalService.value = undefined
  currentDetail.value = undefined
  deliveryInfo.value = null // 重置收货地址信息
  infoNotify('已停止抢购')
}

export function useZfbDialog() {
  return {
    isShowZfbDialog,
    closeZfbDialog,
    currentRow,
    qrcodeUrl,
    additionalService,
    countdownText,
    currentStep,
    currentDetail,
    transferServiceObj,
    additionalServiceSum,
    nextStep,
    deliveryInfo,
    onDeliveryInfoChanged,
  }
}
